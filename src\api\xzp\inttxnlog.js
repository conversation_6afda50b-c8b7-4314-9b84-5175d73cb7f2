import request from '@/utils/request'

// 查询交易流水列表
export function listIntTxnLog(query) {
  return request({
    url: '/api/xzp/intTxnLog/intTxnLogList',
    method: 'post',
    data: query
  })
}

// 查询交易流水列表（关联查询）
export function selectIntTxnLogList(query) {
  return request({
    url: '/api/xzp/intTxnLog/selectIntTxnLogList',
    method: 'post',
    data: query
  })
}

// 查询缴费明细
export function queryTxnLogDetail(query) {
  return request({
    url: '/api/admin/xzp/txn/queryTxnLogDetail',
    method: 'post',
    data: query
  })
}

// 导出缴费明细
export function exportTxnLogDetail(data) {
  return request({
    url: '/api/admin/xzp/txn/exportTxnLogDetail',
    method: 'post',
    data: data,
    responseType: 'blob',
    timeout: 20000
  })
}